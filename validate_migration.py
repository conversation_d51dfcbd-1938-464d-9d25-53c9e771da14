import mysql.connector
from mysql.connector import Error
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MigrationValidator:
    def __init__(self):
        # 数据库连接配置 (从 generate_card_data.py 复制)
        self.db_config = {
            'host': 'nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com',
            'port': 3306,
            'database': 'staging_card',
            'user': 'root',
            'password': 'BwYvW64msQ5Lndu2Umpn',
            'autocommit': True,
            'charset': 'utf8mb4',
            'use_unicode': True,
            'connect_timeout': 60
        }
        
        self.connection = None
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("数据库连接成功")
                return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def get_platform_config_data(self):
        """获取 platform_config 表的数据"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = "SELECT name, title, value FROM platform_config"
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()
            logger.info(f"从 platform_config 表获取到 {len(results)} 条记录")
            return results
        except Error as e:
            logger.error(f"查询 platform_config 表失败: {e}")
            return []
    
    def get_admin_config_data(self):
        """获取 admin_config 表的数据"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            query = "SELECT name, description, value FROM admin_config"
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()
            logger.info(f"从 admin_config 表获取到 {len(results)} 条记录")
            return results
        except Error as e:
            logger.error(f"查询 admin_config 表失败: {e}")
            return []
    
    def validate_migration(self):
        """验证数据迁移是否正确"""
        logger.info("开始验证数据迁移...")
        
        # 获取两个表的数据
        platform_data = self.get_platform_config_data()
        admin_data = self.get_admin_config_data()
        
        if not platform_data:
            logger.warning("platform_config 表没有数据")
            return
        
        if not admin_data:
            logger.error("admin_config 表没有数据，迁移可能失败")
            return
        
        # 将 admin_config 数据转换为字典，便于查找
        # key: (name, description, value), value: 记录
        admin_dict = {}
        for record in admin_data:
            key = (record['name'], record['description'], record['value'])
            admin_dict[key] = record
        
        # 验证结果统计
        total_platform_records = len(platform_data)
        matched_records = 0
        missing_records = []
        value_mismatch_records = []
        
        logger.info(f"开始验证 {total_platform_records} 条 platform_config 记录...")
        
        for platform_record in platform_data:
            platform_name = platform_record['name']
            platform_title = platform_record['title']  # 对应 admin_config.description
            platform_value = platform_record['value']
            
            # 在 admin_config 中查找匹配的记录
            # platform_config.name -> admin_config.name
            # platform_config.title -> admin_config.description  
            # platform_config.value -> admin_config.value
            admin_key = (platform_name, platform_title, platform_value)
            
            if admin_key in admin_dict:
                matched_records += 1
                logger.debug(f"✓ 匹配成功: name='{platform_name}', description='{platform_title}', value='{platform_value}'")
            else:
                # 检查是否存在 name 相同但其他字段不同的记录
                name_matches = [record for record in admin_data if record['name'] == platform_name]
                
                if name_matches:
                    # 存在同名记录，但字段值不匹配
                    for admin_record in name_matches:
                        if admin_record['description'] != platform_title or admin_record['value'] != platform_value:
                            value_mismatch_records.append({
                                'platform': platform_record,
                                'admin': admin_record,
                                'mismatch_type': 'value_different'
                            })
                            break
                else:
                    # 完全没有找到对应记录
                    missing_records.append(platform_record)
        
        # 输出验证结果
        logger.info("\n" + "="*60)
        logger.info("数据迁移验证结果:")
        logger.info("="*60)
        logger.info(f"platform_config 总记录数: {total_platform_records}")
        logger.info(f"admin_config 总记录数: {len(admin_data)}")
        logger.info(f"完全匹配记录数: {matched_records}")
        logger.info(f"缺失记录数: {len(missing_records)}")
        logger.info(f"字段值不匹配记录数: {len(value_mismatch_records)}")
        
        success_rate = (matched_records / total_platform_records) * 100 if total_platform_records > 0 else 0
        logger.info(f"迁移成功率: {success_rate:.2f}%")
        
        # 详细输出问题记录
        if missing_records:
            logger.warning(f"\n缺失的记录 ({len(missing_records)} 条):")
            for i, record in enumerate(missing_records, 1):
                logger.warning(f"  {i}. name='{record['name']}', title='{record['title']}', value='{record['value']}'")
        
        if value_mismatch_records:
            logger.warning(f"\n字段值不匹配的记录 ({len(value_mismatch_records)} 条):")
            for i, mismatch in enumerate(value_mismatch_records, 1):
                platform = mismatch['platform']
                admin = mismatch['admin']
                logger.warning(f"  {i}. name='{platform['name']}'")
                logger.warning(f"     platform_config: title='{platform['title']}', value='{platform['value']}'")
                logger.warning(f"     admin_config: description='{admin['description']}', value='{admin['value']}'")
        
        if matched_records == total_platform_records:
            logger.info("\n🎉 数据迁移验证通过！所有记录都已正确迁移。")
        else:
            logger.error(f"\n❌ 数据迁移验证失败！有 {total_platform_records - matched_records} 条记录存在问题。")
        
        return {
            'total_platform_records': total_platform_records,
            'total_admin_records': len(admin_data),
            'matched_records': matched_records,
            'missing_records': missing_records,
            'value_mismatch_records': value_mismatch_records,
            'success_rate': success_rate
        }

def main():
    """主函数"""
    validator = MigrationValidator()
    
    try:
        # 连接数据库
        if not validator.connect_database():
            return
        
        # 执行验证
        result = validator.validate_migration()
        
        # 根据结果返回适当的退出码
        if result and result['matched_records'] == result['total_platform_records']:
            exit(0)  # 验证成功
        else:
            exit(1)  # 验证失败
            
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
        exit(1)
    finally:
        validator.close_connection()

if __name__ == "__main__":
    main()
